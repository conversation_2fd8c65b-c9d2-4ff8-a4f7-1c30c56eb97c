{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "Node", "declaration": true, "declarationMap": true, "sourceMap": true, "incremental": true, "composite": false, "strict": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@b6/logger": ["packages/logger/src"], "@b6/config": ["packages/config/src"], "@b6/auth": ["packages/auth/src"], "@b6/utils": ["packages/utils/src"]}}, "exclude": ["node_modules", "dist"]}