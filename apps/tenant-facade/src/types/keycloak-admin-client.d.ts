declare module 'keycloak-admin-client' {
  interface KeycloakAdminClientConfig {
    baseUrl: string;
    realmName: string;
  }

  interface AuthConfig {
    grantType: string;
    clientId: string;
    username: string;
    password: string;
  }

  interface UserCredential {
    type: string;
    value: string;
    temporary: boolean;
  }

  interface CreateUserRequest {
    realm: string;
    username: string;
    email: string;
    enabled: boolean;
    credentials: UserCredential[];
  }

  interface UserRepresentation {
    id?: string;
    username?: string;
    email?: string;
    enabled?: boolean;
  }

  interface UsersResource {
    create(user: CreateUserRequest): Promise<UserRepresentation>;
  }

  class KcAdminClient {
    users: UsersResource;
    
    constructor(config: KeycloakAdminClientConfig);
    auth(config: AuthConfig): Promise<void>;
  }

  export = KcAdminClient;
}
