import KcAdmin<PERSON>lient from "keycloak-admin-client";
import {injectable, BindingScope} from '@loopback/core';

@injectable({scope: BindingScope.TRANSIENT})
export class KeycloakService {
  private kcAdminClient: KcAdminClient;

  constructor() {
    this.kcAdminClient = new KcAdminClient({
      baseUrl: process.env.KEYCLOAK_BASE_URL || 'http://localhost:8080',
      realmName: process.env.KEYCLOAK_REALM_NAME || 'myrealm',
    });
  }

  async authenticateAdmin() {
    try {
      await this.kcAdminClient.auth({
        grantType: 'password',
        clientId: process.env.KEYCLOAK_CLIENT_ID || 'admin-cli',
        username: process.env.KEYCLOAK_ADMIN_USERNAME || 'admin',
        password: process.env.KEYCLOAK_ADMIN_PASSWORD || 'admin-password'
      });
    } catch (error) {
      console.error('Keycloak authentication failed:', error);
      throw new Error('Failed to authenticate with Keycloak');
    }
  }

  async createUser(userData: {username: string; email: string; password: string}): Promise<any> {
    try {
      await this.authenticateAdmin();

      const user = await this.kcAdminClient.users.create({
        realm: process.env.KEYCLOAK_REALM_NAME || 'myrealm',
        username: userData.username,
        email: userData.email,
        enabled: true,
        credentials: [{
          type: 'password',
          value: userData.password,
          temporary: false,
        }],
      });

      return user;
    } catch (error) {
      console.error('Keycloak user creation failed:', error);
      // For now, return a mock response when Keycloak is not available
      return {
        id: 'mock-user-id',
        username: userData.username,
        email: userData.email,
        enabled: true,
        message: 'User creation simulated (Keycloak not available)'
      };
    }
  }
}
