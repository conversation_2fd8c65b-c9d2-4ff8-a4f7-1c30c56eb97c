import KeycloakAdminClient from "@keycloak/keycloak-admin-client";


export class KeycloakService {
  private kcAdminClient: KeycloakAdminClient;

  constructor() {
    this.kcAdminClient = new KeycloakAdminClient({
      baseUrl: 'http://localhost:8080', // your Keycloak server
      realmName: 'myrealm',             // your realm
    });
  }

  async authenticateAdmin() {
    await this.kcAdminClient.auth({
      grantType: 'password',
      clientId: 'admin-cli',     // use your client (admin-cli for now)
      username: 'admin',         // Keycloak admin username
      password: 'admin-password' // Keycloak admin password
    });
  }

  async createUser(userData: {username: string; email: string; password: string}) {
    await this.authenticateAdmin();

    const user = await this.kcAdminClient.users.create({
      realm: 'myrealm',
      username: userData.username,
      email: userData.email,
      enabled: true,
      credentials: [{
        type: 'password',
        value: userData.password,
        temporary: false,
      }],
    });

    return user;
  }
}
