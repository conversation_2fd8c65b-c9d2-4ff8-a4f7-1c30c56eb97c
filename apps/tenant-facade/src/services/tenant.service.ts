import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import {OnboardTenantDto} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class TenantService {
  constructor(/* Add @inject to inject parameters */) {}

  async onboard(tenant: OnboardTenantDto): Promise<void> {
    // TODO : Call tenant create API by rest service by @aswathy
    // TODO : Create a keycloack user with tenant id skip tenant id for now to avoid dependency
    // TODO : emit an event to orchastration service to create subdomain for the tenant
  }
}
