import {injectable, BindingScope} from '@loopback/core';
import {OnboardTenantDto} from '../models';
import {KeycloakService} from './keyclock.service';

@injectable({scope: BindingScope.TRANSIENT})
export class TenantService {
  private keycloakService: KeycloakService;

  constructor() {
    this.keycloakService = new KeycloakService();
  }

  async onboard(tenant: OnboardTenantDto): Promise<{message: string; user: any}> {
    // TODO : Call tenant create API by rest service by @aswathy

    // Create a Keycloak user with tenant admin credentials
    const userData = {
      username: tenant.tenantName, // Using tenant name as username
      email: tenant.adminEmail,
      password: tenant.adminPassword,
    };

    const user = await this.keycloakService.createUser(userData);

    // TODO : emit an event to orchestration service to create subdomain for the tenant

    return {message: 'User created successfully', user};
  }
}
