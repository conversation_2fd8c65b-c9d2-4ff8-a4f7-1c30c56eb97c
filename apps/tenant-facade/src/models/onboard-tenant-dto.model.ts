import {Model, model, property} from '@loopback/repository';

@model()
export class OnboardTenantDto extends Model {
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 3,
      maxLength: 50,
      pattern: '^[a-zA-Z0-9_-]+$',
      errorMessage:
        'Tenant name must be alphanumeric with no spaces, 3-50 chars.',
    },
  })
  tenantName: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      format: 'email',
      errorMessage: 'Admin email must be a valid email address.',
    },
  })
  adminEmail: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 8,
      maxLength: 50,
      errorMessage: 'Admin password must be 8-50 chars long.',
    },
  })
  adminPassword: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 10,
      maxLength: 64,
      errorMessage: 'Idempotency key must be between 10 and 64 chars.',
    },
  })
  idempotencyKey: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      minLength: 1,
      maxLength: 63,
      errorMessage: 'Custom Domain Must be between 1 and 63 chars.',
    },
  })
  customDomain?: string;

  constructor(data?: Partial<OnboardTenantDto>) {
    super(data);
  }
}

export interface OnboardTenantDtoRelations {
  // describe navigational properties here
}

export type OnboardTenantDtoWithRelations = OnboardTenantDto &
  OnboardTenantDtoRelations;
