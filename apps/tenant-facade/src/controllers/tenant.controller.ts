import {getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {OnboardTenantDto} from '../models';
import {service} from '@loopback/core';
import {TenantService} from '../services';

const basePath = '/tenants';
export class TenantController {
  constructor(
    @service(TenantService)
    private readonly tenantService: TenantService,
  ) {}

  @post(`${basePath}/onboard`, {
    responses: {
      [200]: {
        description: 'Tenant onboarded successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {type: 'string'},
                user: {type: 'object'},
              },
            },
          },
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        ['application/json']: {
          schema: getModelSchemaRef(OnboardTenantDto, {
            title: 'NewTenant',
          }),
        },
      },
    })
    tenant: OnboardTenantDto,
  ): Promise<{message: string; user: any}> {
    return await this.tenantService.onboard(tenant);
  }
}
