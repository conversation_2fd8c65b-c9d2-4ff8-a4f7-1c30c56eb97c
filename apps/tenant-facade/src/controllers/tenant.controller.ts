import {getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {OnboardTenantDto} from '../models';
import {service} from '@loopback/core';
import {TenantService} from '../services';

const basePath = '/tenants';
export class TenantController {
  constructor(
    @service(TenantService)
    private readonly tenantService: TenantService,
  ) {}

  @post(`${basePath}/onboard`, {
    responses: {
      [204]: {
        description: 'Address model instance',
      },
    },
  })
  async create(
    @requestBody({
      content: {
        ['application/json']: {
          schema: getModelSchemaRef(OnboardTenantDto, {
            title: 'NewTenant',
          }),
        },
      },
    })
    tenant: OnboardTenantDto,
  ): Promise<void> {
    this.tenantService.onboard(tenant);
  }
}
