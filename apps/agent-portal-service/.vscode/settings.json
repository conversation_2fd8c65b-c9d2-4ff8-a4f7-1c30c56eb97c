{
  "editor.rulers": [80],
  "editor.tabCompletion": "on",
  "editor.tabSize": 2,
  "editor.trimAutoWhitespace": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.eslint": true
  },

  "files.exclude": {
    "**/.DS_Store": true,
    "**/.git": true,
    "**/.hg": true,
    "**/.svn": true,
    "**/CVS": true,
    "dist": true,
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,

  "typescript.tsdk": "./node_modules/typescript/lib",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false,
  "typescript.preferences.quoteStyle": "single",
  "eslint.run": "onSave",
  "eslint.nodePath": "./node_modules",
  "eslint.validate": [
    "javascript",
    "typescript"
  ]
}
