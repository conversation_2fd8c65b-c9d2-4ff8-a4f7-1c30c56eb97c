{"name": "subscription-service", "version": "0.0.1", "description": "subscription service to manage plans and subscriptions", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "20 || 22 || 24"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t subscription-service .", "docker:run": "docker run -p 3000:3000 -d subscription-service", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@loopback/boot": "^8.0.2", "@loopback/core": "^7.0.1", "@loopback/repository": "^8.0.1", "@loopback/rest": "^15.0.2", "@loopback/rest-explorer": "^8.0.2", "@loopback/service-proxy": "^8.0.1", "tslib": "^2.0.0"}, "devDependencies": {"@loopback/build": "^12.0.1", "source-map-support": "^0.5.21", "@loopback/testlab": "^8.0.1", "@types/node": "^16.18.126", "@loopback/eslint-config": "^16.0.0", "eslint": "^8.57.1", "typescript": "~5.2.2"}}