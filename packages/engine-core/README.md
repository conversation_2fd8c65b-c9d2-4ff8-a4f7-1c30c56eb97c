# engine-core

[![LoopBack](https://github.com/loopbackio/loopback-next/raw/master/docs/site/imgs/branding/Powered-by-LoopBack-Badge-(blue)-@2x.png)](http://loopback.io/)

## Installation

Install EngineCoreComponent using `npm`;

```sh
$ [npm install | yarn add] engine-core
```

## Basic Use

Configure and load EngineCoreComponent in the application constructor
as shown below.

```ts
import {EngineCoreComponent, EngineCoreComponentOptions, DEFAULT_ENGINE_CORE_OPTIONS} from 'engine-core';
// ...
export class MyApplication extends BootMixin(ServiceMixin(RepositoryMixin(RestApplication))) {
  constructor(options: ApplicationConfig = {}) {
    const opts: EngineCoreComponentOptions = DEFAULT_ENGINE_CORE_OPTIONS;
    this.configure(EngineCoreComponentBindings.COMPONENT).to(opts);
      // Put the configuration options here
    });
    this.component(EngineCoreComponent);
    // ...
  }
  // ...
}
```
