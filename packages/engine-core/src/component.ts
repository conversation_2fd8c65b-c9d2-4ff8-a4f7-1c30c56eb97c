import {
  Application,
  injectable,
  Component,
  config,
  ContextTags,
  CoreBindings,
  inject,
} from '@loopback/core';
import {EngineCoreComponentBindings} from './keys'
import {DEFAULT_ENGINE_CORE_OPTIONS, EngineCoreComponentOptions} from './types';

// Configure the binding for EngineCoreComponent
@injectable({tags: {[ContextTags.KEY]: EngineCoreComponentBindings.COMPONENT}})
export class EngineCoreComponent implements Component {
  constructor(
    @inject(CoreBindings.APPLICATION_INSTANCE)
    private application: Application,
    @config()
    private options: EngineCoreComponentOptions = DEFAULT_ENGINE_CORE_OPTIONS,
  ) {}
}
