{"name": "migrations", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"db:migrate": "run-s db:migrate:*", "db:migrate-down": "run-s db:migrate-down:*", "db:migrate-reset": "run-s db:migrate-reset:*", "db:migrate:subscription": "db-migrate up --config 'subscription-service/database.json' -m 'subscription-service/migrations'", "db:migrate-down:subscription": "db-migrate down --config 'subscription-service/database.json' -m 'subscription-service/migrations'", "db:migrate-reset:subscription": "db-migrate reset --config 'subscription-service/database.json' -m 'subscription-service/migrations'", "db:migrate:tenant": "db-migrate up --config 'tenant-service/database.json' -m 'tenant-service/migrations'", "db:migrate-down:tenant": "db-migrate down --config 'tenant-service/database.json' -m 'tenant-service/migrations'", "db:migrate-reset:tenant": "db-migrate reset --config 'tenant-service/database.json' -m 'tenant-service/migrations'"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"db-migrate": "^0.11.14", "db-migrate-pg": "^1.5.2", "dotenv": "^17.2.2", "dotenv-extended": "^2.9.0"}, "devDependencies": {"npm-run-all": "^4.1.5"}}