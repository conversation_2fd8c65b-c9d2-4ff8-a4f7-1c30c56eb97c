CREATE SCHEMA IF NOT EXISTS main;

SET search_path TO main,public;
GRANT ALL ON SCHEMA main TO public;

CREATE TABLE IF NOT EXISTS main.plans (
    id                     uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    plan_code              varchar(50) NOT NULL, -- internal code (e.g. "BOT_BUILDER_BASIC")
    name                   varchar(100) NOT NULL,
    description            text,
    price_cents            integer NOT NULL, -- price in cents for accuracy
    currency               varchar(10) DEFAULT 'USD' NOT NULL,
    billing_interval       varchar(20) DEFAULT 'monthly' NOT NULL, -- monthly, yearly, etc.
    features               jsonb, -- store feature limits/config
    gateway_name           varchar(50) NOT NULL, -- e.g., 'stripe', 'chargebee'
    gateway_ref_id         varchar(100) NOT NULL, -- plan id in gateway
    created_on             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on            timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                bool DEFAULT false NOT NULL,
    deleted_on             timestamptz,
    deleted_by             uuid,
    CONSTRAINT pk_plans_id PRIMARY KEY (id),
    CONSTRAINT uq_plans_plan_code UNIQUE (plan_code)
);

CREATE TABLE IF NOT EXISTS main.subscriptions (
    id                     uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    tenant_id              uuid NOT NULL, -- subscriber (tenant)
    plan_id                uuid NOT NULL REFERENCES main.plans(id),
    status                 varchar(20) DEFAULT 'active' NOT NULL, -- active, trialing, canceled, past_due
    start_date             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    end_date               timestamptz,
    trial_end_date         timestamptz,
    renewal_date           timestamptz,
    gateway_name           varchar(50) NOT NULL, -- stripe, chargebee, etc.
    gateway_ref_id         varchar(100) NOT NULL, -- subscription id from gateway
    created_on             timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on            timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted                bool DEFAULT false NOT NULL,
    deleted_on             timestamptz,
    deleted_by             uuid,
    CONSTRAINT pk_subscriptions_id PRIMARY KEY (id)
);

-- Indexes
CREATE INDEX idx_plans_gateway_name ON main.plans(gateway_name);
CREATE INDEX idx_plans_gateway_ref_id ON main.plans(gateway_ref_id);
CREATE INDEX idx_plans_billing_interval ON main.plans(billing_interval);
CREATE INDEX idx_plans_deleted ON main.plans(deleted);

CREATE INDEX idx_subscriptions_tenant_id ON main.subscriptions(tenant_id);
CREATE INDEX idx_subscriptions_plan_id ON main.subscriptions(plan_id);
CREATE INDEX idx_subscriptions_status ON main.subscriptions(status);
CREATE INDEX idx_subscriptions_gateway_name ON main.subscriptions(gateway_name);
CREATE INDEX idx_subscriptions_gateway_ref_id ON main.subscriptions(gateway_ref_id);
CREATE INDEX idx_subscriptions_deleted ON main.subscriptions(deleted);


-- Function
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.modified_on = now();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Attach trigger to plans
CREATE TRIGGER trg_plans_set_modified
BEFORE UPDATE ON main.plans
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Attach trigger to subscriptions
CREATE TRIGGER trg_subscriptions_set_modified
BEFORE UPDATE ON main.subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();